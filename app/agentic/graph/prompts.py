import json
from datetime import date, datetime
from typing import Any

from langchain_core.messages import SystemMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langfuse import <PERSON><PERSON>

from app.common.helpers.to_async import to_async


class AgentPrompts:
    def __init__(self, langfuse_client: Langfuse):
        self.langfuse = langfuse_client

    @staticmethod
    def _create_prompt_template(system_message: str, name: str) -> ChatPromptTemplate:
        return ChatPromptTemplate.from_messages(
            [
                SystemMessage(content=system_message, name=name),
                MessagesPlaceholder(variable_name="messages"),
            ]
        )

    @to_async
    def _get_prompt_string(self, prompt_name: str) -> str:
        prompt_raw = self.langfuse.get_prompt(prompt_name)
        return prompt_raw.get_langchain_prompt()

    async def get_supervisor_system_prompt(self) -> ChatPromptTemplate:
        prompt = await self._get_prompt_string("supervisor")
        return self._create_prompt_template(prompt, "supervisor_prompt")

    async def get_sales_document_agent_prompt(self) -> ChatPromptTemplate:
        prompt = await self._get_prompt_string("sales_document_agent")
        return self._create_prompt_template(prompt, "sales_document_agent_prompt")

    async def format_account_context_message(
        self, account_info: dict[str, Any]
    ) -> SystemMessage:
        prompt = await self._get_prompt_string("account_context")
        formatted_account_info = self._safe_json_dumps(account_info, indent=2)
        final_content = prompt.format(account_info=formatted_account_info)
        return SystemMessage(content=final_content, name="account_context_prompt")

    def _serialize_datetime_objects(self, obj: Any) -> Any:
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, date):
            return obj.isoformat()
        elif isinstance(obj, dict):
            return {
                key: self._serialize_datetime_objects(value)
                for key, value in obj.items()
            }
        elif isinstance(obj, list):
            return [self._serialize_datetime_objects(item) for item in obj]
        else:
            return obj

    def _safe_json_dumps(self, obj: Any, **kwargs) -> str:
        serialized_obj = self._serialize_datetime_objects(obj)
        return json.dumps(serialized_obj, **kwargs)
